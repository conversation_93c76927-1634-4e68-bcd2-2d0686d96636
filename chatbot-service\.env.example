# =================================
# ATMA Chatbot Service Configuration
# =================================

# Server Configuration
NODE_ENV=development
PORT=3006
HOST=0.0.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=your_secure_password
DB_SCHEMA=chat

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Auth Service Configuration
AUTH_SERVICE_URL=http://localhost:3001

# Archive Service Configuration (Phase 3)
ARCHIVE_SERVICE_URL=http://localhost:3002

# Assessment Integration Configuration (Phase 3)
ENABLE_ASSESSMENT_INTEGRATION=true
ENABLE_PERSONALIZED_WELCOME_MESSAGES=true
ENABLE_SUGGESTED_QUESTIONS=true

# RabbitMQ Configuration (Phase 3) - aligned with analysis-worker
RABBITMQ_URL=amqp://localhost:5672
EVENTS_EXCHANGE_NAME=atma_events_exchange
CHATBOT_EVENTS_QUEUE=chatbot_analysis_events
RABBITMQ_CONNECTION_TIMEOUT=10000
RABBITMQ_RECONNECT_DELAY=5000
RABBITMQ_MAX_RECONNECT_ATTEMPTS=10

# OpenRouter Configuration
OPENROUTER_API_KEY=OPENROUTER_API_KEY_GOES_HERE
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_MODEL=qwen/qwen3-235b-a22b-07-25:free
FALLBACK_MODEL=meta-llama/llama-3.2-3b-instruct:free
EMERGENCY_FALLBACK_MODEL=openai/gpt-4o-mini
USE_FREE_MODELS_ONLY=true
MAX_TOKENS=1000
TEMPERATURE=0.3
OPENROUTER_TIMEOUT=45000

# Rate Limiting for Free Models
FREE_MODEL_RATE_LIMIT_PER_MINUTE=20
MAX_CONVERSATION_HISTORY_TOKENS=6000

# Rate Limiting General
RATE_LIMIT_CONVERSATIONS_PER_DAY=100

# General Chatbot Settings
MAX_MESSAGE_LENGTH=4000

# OpenRouter Headers
HTTP_REFERER=https://atma.chhrone.web.id
X_TITLE=ATMA - AI Talent Mapping Assessment

# Rate Limiting Configuration
# Free Model Rate Limiting
FREE_MODEL_RATE_WINDOW_MS=60000
FREE_MODEL_RATE_LIMIT_PER_MINUTE=20

# Message Rate Limiting
MESSAGE_RATE_WINDOW_MS=300000
MESSAGE_RATE_LIMIT_PER_5MIN=50

# Burst Protection
BURST_PROTECTION_WINDOW_MS=10000
BURST_PROTECTION_LIMIT=5

# General API Rate Limiting
API_RATE_WINDOW_MS=900000
API_RATE_LIMIT_PER_15MIN=1000

# Message Configuration
MAX_MESSAGE_LENGTH=10000
MAX_CONVERSATION_HISTORY_TOKENS=6000

# CORS Configuration
ALLOWED_ORIGINS=*

# Internal Service Authentication
INTERNAL_SERVICE_KEY=your_internal_service_key_here

# Redis Configuration (for rate limiting and caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Metrics Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Development Configuration
ENABLE_REQUEST_LOGGING=true
ENABLE_QUERY_LOGGING=false
ENABLE_DEBUG_LOGS=false

# Production Configuration (uncomment for production)
# NODE_ENV=production
# LOG_LEVEL=warn
# ENABLE_DEBUG_LOGS=false
# ENABLE_QUERY_LOGGING=false

# Security Configuration
HELMET_ENABLED=true
TRUST_PROXY=false

# Performance Configuration
REQUEST_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000
HEADERS_TIMEOUT=60000

# OpenRouter Model Configuration
# Free models available (as of 2024)
# - qwen/qwen-2.5-coder-32b-instruct:free
# - meta-llama/llama-3.2-3b-instruct:free
# - microsoft/phi-3-mini-128k-instruct:free
# - huggingface/zephyr-7b-beta:free

# Paid models for emergency fallback
# - openai/gpt-4o-mini
# - openai/gpt-3.5-turbo
# - anthropic/claude-3-haiku

# Feature Flags
ENABLE_CONVERSATION_HISTORY_OPTIMIZATION=true
ENABLE_TOKEN_USAGE_TRACKING=true
ENABLE_MODEL_FALLBACK=true
ENABLE_BURST_PROTECTION=true
ENABLE_FREE_MODEL_PRIORITY=true

# Phase 3 Feature Flags
ENABLE_ASSESSMENT_INTEGRATION=true
ENABLE_EVENT_DRIVEN_CONVERSATIONS=true
ENABLE_PERSONALIZED_WELCOME_MESSAGES=true
ENABLE_SUGGESTED_QUESTIONS=true
ENABLE_ASSESSMENT_CONTEXT_OPTIMIZATION=true

# Monitoring and Alerting
ENABLE_ERROR_ALERTS=false
ALERT_WEBHOOK_URL=
ALERT_THRESHOLD_ERROR_RATE=0.05
ALERT_THRESHOLD_RESPONSE_TIME=5000

# Backup and Recovery
ENABLE_CONVERSATION_BACKUP=false
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=30
