const { Conversation, Message } = require('../models');
const QueueService = require('./queueService');
const ContextService = require('./contextService');
const OpenRouterService = require('./openrouterService');
const logger = require('../utils/logger');

/**
 * AssessmentEventHandler for processing assessment completion events
 * Handles auto-conversation creation and personalized welcome messages
 */
class AssessmentEventHandler {
  constructor() {
    this.queueService = new QueueService();
    this.contextService = new ContextService();
    this.openrouterService = new OpenRouterService();
    this.isInitialized = false;
  }

  /**
   * Initialize the event handler
   */
  async initialize() {
    try {
      if (!process.env.ENABLE_ASSESSMENT_INTEGRATION || process.env.ENABLE_ASSESSMENT_INTEGRATION !== 'true') {
        logger.info('Assessment integration disabled, skipping event handler initialization');
        return false;
      }

      logger.info('Initializing AssessmentEventHandler');

      // Initialize queue service
      await this.queueService.initialize();

      // Subscribe to analysis.completed events from analysis-worker
      await this.queueService.subscribe('analysis.completed', this.handleAssessmentComplete.bind(this));

      this.isInitialized = true;
      logger.info('AssessmentEventHandler initialized successfully');
      return true;
    } catch (error) {
      logger.error('Failed to initialize AssessmentEventHandler', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Handle assessment completion event from analysis-worker
   * @param {Object} eventData - Event data from RabbitMQ
   */
  async handleAssessmentComplete(eventData) {
    try {
      const { eventType, userId, resultId, jobId, userEmail, metadata } = eventData;

      logger.info('Processing analysis completion event', {
        eventType,
        userId,
        resultId,
        jobId,
        userEmail,
        assessmentName: metadata?.assessmentName
      });

      // Validate event data
      if (!userId || !resultId) {
        logger.error('Invalid event data: missing userId or resultId', {
          eventData
        });
        return;
      }

      // Fetch assessment data from archive service using resultId
      const assessmentData = await this.fetchAssessmentDataFromArchive(resultId);

      if (!assessmentData) {
        logger.error('Failed to fetch assessment data from archive service', {
          resultId,
          userId
        });
        return;
      }

      // Check if assessment conversation already exists for this result
      const existingConversation = await this.findAssessmentConversation(userId, resultId);

      if (existingConversation) {
        logger.info('Assessment conversation already exists, updating context', {
          conversationId: existingConversation.id,
          userId,
          resultId
        });

        // Update existing conversation with new assessment data
        await this.updateConversationContext(existingConversation.id, assessmentData);
      } else {
        logger.info('Creating new assessment conversation', {
          userId,
          resultId,
          assessmentName: metadata?.assessmentName
        });

        // Create new assessment-based conversation
        await this.createAssessmentConversation(userId, resultId, assessmentData);
      }

      logger.info('Analysis completion event processed successfully', {
        userId,
        resultId,
        jobId
      });

    } catch (error) {
      logger.error('Error handling analysis completion event', {
        eventData,
        error: error.message,
        stack: error.stack
      });

      // Don't throw error to avoid message requeue loops
      // Consider implementing dead letter queue for failed events
    }
  }

  /**
   * Fetch assessment data from archive service using resultId
   * @param {string} resultId - Analysis result ID
   * @returns {Object|null} Assessment data or null
   */
  async fetchAssessmentDataFromArchive(resultId) {
    try {
      const axios = require('axios');
      const archiveServiceUrl = process.env.ARCHIVE_SERVICE_URL || 'http://archive-service:3004';
      const internalServiceKey = process.env.INTERNAL_SERVICE_KEY || 'default-internal-key';

      logger.info('Fetching assessment data from archive service', { resultId });

      const response = await axios.get(
        `${archiveServiceUrl}/archive/results?id=${resultId}&limit=1`,
        {
          headers: {
            'x-internal-service': 'true',
            'x-service-key': internalServiceKey,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      if (response.data.success && response.data.data) {
        logger.info('Assessment data fetched successfully', {
          resultId,
          hasPersonaProfile: !!response.data.data.persona_profile,
          hasAssessmentData: !!response.data.data.assessment_data
        });

        return response.data.data;
      }

      logger.warn('Assessment data not found or invalid response', {
        resultId,
        success: response.data.success
      });
      return null;
    } catch (error) {
      logger.error('Failed to fetch assessment data from archive service', {
        resultId,
        error: error.message,
        status: error.response?.status
      });
      return null;
    }
  }

  /**
   * Find existing assessment conversation
   * @param {string} userId - User ID
   * @param {string} resultId - Analysis result ID (used as assessment_id)
   * @returns {Object|null} Existing conversation or null
   */
  async findAssessmentConversation(userId, resultId) {
    try {
      const conversation = await Conversation.findOne({
        where: {
          user_id: userId,
          context_type: 'assessment',
          status: 'active'
        },
        order: [['created_at', 'DESC']]
      });

      // Check if conversation is for this specific assessment result
      if (conversation && conversation.context_data?.assessment_id === resultId) {
        return conversation;
      }

      return null;
    } catch (error) {
      logger.error('Error finding assessment conversation', {
        userId,
        resultId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Create new assessment-based conversation
   * @param {string} userId - User ID
   * @param {string} resultId - Analysis result ID (used as assessment_id)
   * @param {Object} assessmentData - Assessment data from archive service
   * @returns {Object} Created conversation and welcome message
   */
  async createAssessmentConversation(userId, resultId, assessmentData) {
    try {
      // Create conversation with assessment context
      const conversation = await Conversation.create({
        user_id: userId,
        title: 'Career Guidance - Assessment Results',
        context_type: 'assessment',
        context_data: {
          assessment_id: resultId, // Use resultId as assessment_id
          analysis_results: assessmentData,
          persona_profile: assessmentData.persona_profile,
          assessment_data: assessmentData.assessment_data,
          created_from_event: true,
          event_timestamp: new Date().toISOString()
        },
        metadata: {
          auto_generated: true,
          assessment_date: assessmentData.created_at || new Date().toISOString(),
          source: 'analysis_complete_event',
          result_id: resultId
        }
      });

      logger.info('Assessment conversation created', {
        conversationId: conversation.id,
        userId,
        resultId,
        hasPersonaProfile: !!assessmentData.persona_profile
      });

      // Generate personalized welcome message if enabled
      let welcomeMessage = null;
      let suggestions = [];

      if (process.env.ENABLE_PERSONALIZED_WELCOME_MESSAGES === 'true') {
        try {
          welcomeMessage = await this.generateAssessmentWelcomeMessage(
            conversation.id,
            assessmentData
          );

          // Save welcome message
          await Message.create({
            conversation_id: conversation.id,
            sender_type: 'assistant',
            content: welcomeMessage,
            metadata: {
              type: 'assessment_welcome',
              auto_generated: true,
              generated_at: new Date().toISOString()
            }
          });

          logger.info('Welcome message generated and saved', {
            conversationId: conversation.id,
            messageLength: welcomeMessage.length
          });
        } catch (error) {
          logger.error('Failed to generate welcome message', {
            conversationId: conversation.id,
            error: error.message
          });
        }
      }

      // Generate suggested questions if enabled
      if (process.env.ENABLE_SUGGESTED_QUESTIONS === 'true') {
        try {
          suggestions = await this.generateAssessmentSuggestions(analysisResults);
          
          logger.info('Suggested questions generated', {
            conversationId: conversation.id,
            suggestionCount: suggestions.length
          });
        } catch (error) {
          logger.error('Failed to generate suggestions', {
            conversationId: conversation.id,
            error: error.message
          });
        }
      }

      return { 
        conversation, 
        welcomeMessage, 
        suggestions 
      };
    } catch (error) {
      logger.error('Error creating assessment conversation', {
        userId,
        assessmentId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Update existing conversation context
   * @param {string} conversationId - Conversation ID
   * @param {Object} analysisResults - New analysis results
   */
  async updateConversationContext(conversationId, analysisResults) {
    try {
      const conversation = await Conversation.findByPk(conversationId);
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      // Update context data with new analysis results
      const updatedContextData = {
        ...conversation.context_data,
        analysis_results: analysisResults,
        updated_from_event: true,
        last_update_timestamp: new Date().toISOString()
      };

      await conversation.update({
        context_data: updatedContextData,
        updated_at: new Date()
      });

      logger.info('Conversation context updated', {
        conversationId,
        assessmentId: conversation.context_data.assessment_id
      });
    } catch (error) {
      logger.error('Error updating conversation context', {
        conversationId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate personalized welcome message
   * @param {string} conversationId - Conversation ID
   * @param {Object} analysisResults - Assessment analysis results
   * @returns {string} Generated welcome message
   */
  async generateAssessmentWelcomeMessage(conversationId, analysisResults) {
    try {
      const assessmentSummary = this.contextService.summarizeAssessmentData(analysisResults);
      
      const systemPrompt = `You are a career advisor AI. Generate a warm, personalized welcome message for a user who just completed their career assessment.

Assessment Summary: ${assessmentSummary}

Create a welcome message that:
1. Acknowledges their assessment completion
2. Highlights 2-3 key insights from their results
3. Expresses enthusiasm about helping with career guidance
4. Invites them to ask questions

Keep it conversational, encouraging, and under 150 words.`;

      const messages = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: 'Generate my personalized welcome message based on my assessment results.' }
      ];

      const response = await this.openrouterService.generateResponse(messages, {
        maxTokens: 200,
        temperature: 0.8,
        userId: 'system',
        conversationId: conversationId
      });

      return response.content;
    } catch (error) {
      logger.error('Error generating welcome message', {
        conversationId,
        error: error.message
      });
      
      // Return fallback message
      return "Welcome! I've reviewed your career assessment results and I'm excited to help you explore career opportunities that align with your unique personality and interests. Feel free to ask me anything about your results or career guidance!";
    }
  }

  /**
   * Generate assessment-based suggested questions
   * @param {Object} analysisResults - Assessment analysis results
   * @returns {Array} Array of suggested questions
   */
  async generateAssessmentSuggestions(analysisResults) {
    try {
      const riasecTop = this.contextService.getTopRiasecTypes(analysisResults.riasec || {});
      const oceanTop = this.contextService.getTopOceanTraits(analysisResults.ocean || {});
      
      const suggestions = [
        `What career paths align with my ${riasecTop[0]} and ${riasecTop[1]} interests?`,
        `How can I leverage my high ${oceanTop[0]} trait in my career?`,
        `What are my strongest personality traits for leadership roles?`,
        `Based on my assessment, what skills should I develop?`,
        `What work environments would suit my personality best?`,
        `How do my values align with different career options?`,
        `What are some specific job roles that match my profile?`,
        `How can I use my character strengths in my career?`
      ];

      // Return top 4 suggestions
      return suggestions.slice(0, 4);
    } catch (error) {
      logger.error('Error generating suggestions', {
        error: error.message
      });
      
      // Return fallback suggestions
      return [
        "What career paths would be best for my personality?",
        "How can I use my strengths in my career?",
        "What skills should I focus on developing?",
        "What work environments suit me best?"
      ];
    }
  }

  /**
   * Close the event handler gracefully
   */
  async close() {
    try {
      if (this.queueService) {
        await this.queueService.close();
      }
      
      this.isInitialized = false;
      logger.info('AssessmentEventHandler closed successfully');
    } catch (error) {
      logger.error('Error closing AssessmentEventHandler', {
        error: error.message
      });
    }
  }

  /**
   * Get handler status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      queueStatus: this.queueService ? this.queueService.getStatus() : null
    };
  }
}

module.exports = AssessmentEventHandler;
